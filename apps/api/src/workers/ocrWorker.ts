import { PubSub, Subscription, Message } from '@google-cloud/pubsub';
import { OcrOrchestrator } from '../services/ocr/orchestrator';
import { OcrProcessingOptions } from '../services/ocr/types';
import { logger } from '../config/logger';
import { env } from '../config/env';

/**
 * OCR Worker Service
 * 
 * Standalone service that processes OCR jobs from Pub/Sub messages.
 * This worker can be deployed as a separate Cloud Run service that scales
 * independently based on the OCR processing load.
 */
export class OcrWorker {
  private pubsub: PubSub;
  private subscription: Subscription;
  private orchestrator: OcrOrchestrator;
  private isRunning = false;

  constructor() {
    this.pubsub = new PubSub();
    this.orchestrator = new OcrOrchestrator();
    
    // Get subscription name from environment
    const subscriptionName = env.PUBSUB_INVOICE_PROCESSING_SUBSCRIPTION;
    this.subscription = this.pubsub.subscription(subscriptionName);
    
    // Configure subscription settings
    this.subscription.setOptions({
      flowControlSettings: {
        maxMessages: 10, // Process up to 10 messages concurrently
        allowExcessMessages: false
      },
      ackDeadline: 300 // 5 minutes to process each message
    });
  }

  /**
   * Start the OCR worker
   */
  public start(): void {
    if (this.isRunning) {
      logger.warn('OCR worker is already running');
      return;
    }

    logger.info('Starting OCR worker...');
    
    // Set up message handler
    this.subscription.on('message', this.handleMessage.bind(this));
    
    // Set up error handler
    this.subscription.on('error', (error) => {
      logger.error('OCR worker subscription error:', error);
    });

    this.isRunning = true;
    logger.info(`OCR worker started, listening to subscription: ${this.subscription.name}`);
  }

  /**
   * Stop the OCR worker
   */
  public stop(): void {
    if (!this.isRunning) {
      logger.warn('OCR worker is not running');
      return;
    }

    logger.info('Stopping OCR worker...');
    this.subscription.removeAllListeners();
    this.isRunning = false;
    logger.info('OCR worker stopped');
  }

  /**
   * Handle incoming Pub/Sub messages
   */
  private async handleMessage(message: Message): Promise<void> {
    const startTime = Date.now();
    const messageId = message.id;
    
    logger.info(`Processing OCR message: ${messageId}`);

    try {
      // Parse message data
      const messageData = JSON.parse(message.data.toString());
      
      // Validate required fields
      const { jobId, invoiceId, tenantId, imageUrl, originalFilename } = messageData;
      
      if (!jobId || !invoiceId || !tenantId || !imageUrl) {
        throw new Error(`Missing required fields in message ${messageId}`);
      }

      // Create processing options
      const options: OcrProcessingOptions = {
        invoiceId,
        tenantId,
        imageUrl,
        originalFilename: originalFilename || 'unknown',
        vendorId: messageData.vendorId,
        startingTier: messageData.startingTier,
        forceAllTiers: messageData.forceAllTiers || false
      };

      logger.info(`Starting OCR processing for invoice ${invoiceId} (job: ${jobId})`);

      // Process the invoice with the orchestrator
      const result = await this.orchestrator.processInvoice(options);

      // Log processing time
      const processingTime = Date.now() - startTime;
      logger.info(`Completed OCR processing for invoice ${invoiceId} in ${processingTime}ms`);

      // Log result summary
      if (result.success) {
        logger.info(`OCR processing successful for invoice ${invoiceId}`, {
          successfulTier: result.successfulTier,
          confidence: result.confidence,
          extractedFields: Object.keys(result.extractedData || {})
        });
      } else {
        logger.error(`OCR processing failed for invoice ${invoiceId}:`, result.error);
      }

      // Acknowledge the message
      message.ack();
      
    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.error(`Error processing OCR message ${messageId} (${processingTime}ms):`, error);
      
      // Nack the message to retry (with exponential backoff)
      message.nack();
    }
  }

  /**
   * Get worker status
   */
  public getStatus(): { isRunning: boolean; subscriptionName: string } {
    return {
      isRunning: this.isRunning,
      subscriptionName: this.subscription.name
    };
  }
}

/**
 * Create and export a singleton OCR worker instance
 */
export const ocrWorker = new OcrWorker();

/**
 * Graceful shutdown handler
 */
const shutdown = () => {
  logger.info('Received shutdown signal, stopping OCR worker...');
  ocrWorker.stop();
  process.exit(0);
};

// Handle termination signals
process.on('SIGTERM', shutdown);
process.on('SIGINT', shutdown);

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception in OCR worker:', error);
  ocrWorker.stop();
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled rejection in OCR worker:', reason);
  ocrWorker.stop();
  process.exit(1);
});
