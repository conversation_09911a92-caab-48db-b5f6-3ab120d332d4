/// <reference types="jest" />

import { OcrWorker } from '../ocrWorker';
import { OcrOrchestrator } from '../../services/ocr/orchestrator';
import { OcrTier } from '@billsnapp/shared-types';

// Mock dependencies
jest.mock('../../services/ocr/orchestrator');
jest.mock('../../config/env', () => ({
  env: {
    PUBSUB_INVOICE_PROCESSING_SUBSCRIPTION: 'test-subscription'
  }
}));

jest.mock('@google-cloud/pubsub', () => {
  const mockSubscription = {
    name: 'test-subscription',
    setOptions: jest.fn(),
    on: jest.fn(),
    removeAllListeners: jest.fn()
  };

  const mockPubSub = {
    subscription: jest.fn().mockReturnValue(mockSubscription)
  };

  return {
    PubSub: jest.fn().mockImplementation(() => mockPubSub)
  };
});

// Mock Message
const createMockMessage = (data: any, id = 'test-message-id') => ({
  id,
  data: Buffer.from(JSON.stringify(data)),
  ack: jest.fn(),
  nack: jest.fn()
});

// Mock OCR Orchestrator
const mockOrchestratorProcessInvoice = jest.fn();
(OcrOrchestrator as jest.MockedClass<typeof OcrOrchestrator>).mockImplementation(() => ({
  processInvoice: mockOrchestratorProcessInvoice
} as any));

describe('OcrWorker', () => {
  let worker: OcrWorker;
  let messageHandler: (message: any) => Promise<void>;
  let mockSubscription: any;

  beforeEach(() => {
    jest.clearAllMocks();

    // Get the mocked subscription from the PubSub mock
    const { PubSub } = require('@google-cloud/pubsub');
    const pubsubInstance = new PubSub();
    mockSubscription = pubsubInstance.subscription('test-subscription');

    // Capture the message handler when it's registered
    mockSubscription.on.mockImplementation((event: string, handler: any) => {
      if (event === 'message') {
        messageHandler = handler;
      }
    });

    worker = new OcrWorker();
  });

  afterEach(() => {
    if (worker.getStatus().isRunning) {
      worker.stop();
    }
  });

  describe('start/stop', () => {
    test('should start the worker successfully', () => {
      worker.start();
      
      expect(worker.getStatus().isRunning).toBe(true);
      expect(mockSubscription.setOptions).toHaveBeenCalledWith({
        flowControlSettings: {
          maxMessages: 10,
          allowExcessMessages: false
        },
        ackDeadline: 300
      });
      expect(mockSubscription.on).toHaveBeenCalledWith('message', expect.any(Function));
      expect(mockSubscription.on).toHaveBeenCalledWith('error', expect.any(Function));
    });

    test('should stop the worker successfully', () => {
      worker.start();
      worker.stop();
      
      expect(worker.getStatus().isRunning).toBe(false);
      expect(mockSubscription.removeAllListeners).toHaveBeenCalled();
    });

    test('should not start if already running', () => {
      worker.start();
      const firstCallCount = mockSubscription.on.mock.calls.length;
      
      worker.start(); // Try to start again
      
      expect(mockSubscription.on.mock.calls.length).toBe(firstCallCount);
    });
  });

  describe('message handling', () => {
    beforeEach(() => {
      worker.start();
    });

    test('should process valid OCR message successfully', async () => {
      const messageData = {
        jobId: 'test-job-123',
        invoiceId: 'invoice-456',
        tenantId: 'tenant-789',
        imageUrl: 'gs://bucket/image.jpg',
        originalFilename: 'invoice.pdf',
        vendorId: 'vendor-123',
        startingTier: OcrTier.TIER_1,
        forceAllTiers: false
      };

      const mockMessage = createMockMessage(messageData);
      
      // Mock successful OCR processing
      mockOrchestratorProcessInvoice.mockResolvedValue({
        success: true,
        successfulTier: OcrTier.TIER_1,
        extractedData: {
          vendorName: 'Test Vendor',
          invoiceNumber: 'INV-123',
          totalAmount: 100.00
        },
        confidence: {
          vendorName: 0.95,
          invoiceNumber: 0.9,
          totalAmount: 0.95
        }
      });

      // Process the message
      await messageHandler(mockMessage);

      // Verify OCR orchestrator was called with correct options
      expect(mockOrchestratorProcessInvoice).toHaveBeenCalledWith({
        invoiceId: 'invoice-456',
        tenantId: 'tenant-789',
        imageUrl: 'gs://bucket/image.jpg',
        originalFilename: 'invoice.pdf',
        vendorId: 'vendor-123',
        startingTier: OcrTier.TIER_1,
        forceAllTiers: false
      });

      // Verify message was acknowledged
      expect(mockMessage.ack).toHaveBeenCalled();
      expect(mockMessage.nack).not.toHaveBeenCalled();
    });

    test('should handle missing required fields', async () => {
      const messageData = {
        jobId: 'test-job-123',
        // Missing invoiceId, tenantId, imageUrl
        originalFilename: 'invoice.pdf'
      };

      const mockMessage = createMockMessage(messageData);

      // Process the message
      await messageHandler(mockMessage);

      // Verify OCR orchestrator was not called
      expect(mockOrchestratorProcessInvoice).not.toHaveBeenCalled();

      // Verify message was nacked (for retry)
      expect(mockMessage.nack).toHaveBeenCalled();
      expect(mockMessage.ack).not.toHaveBeenCalled();
    });

    test('should handle OCR processing failures', async () => {
      const messageData = {
        jobId: 'test-job-123',
        invoiceId: 'invoice-456',
        tenantId: 'tenant-789',
        imageUrl: 'gs://bucket/image.jpg'
      };

      const mockMessage = createMockMessage(messageData);
      
      // Mock OCR processing failure
      mockOrchestratorProcessInvoice.mockResolvedValue({
        success: false,
        error: 'All OCR tiers failed'
      });

      // Process the message
      await messageHandler(mockMessage);

      // Verify OCR orchestrator was called
      expect(mockOrchestratorProcessInvoice).toHaveBeenCalled();

      // Verify message was still acknowledged (failed processing is not a retry case)
      expect(mockMessage.ack).toHaveBeenCalled();
      expect(mockMessage.nack).not.toHaveBeenCalled();
    });

    test('should handle orchestrator exceptions', async () => {
      const messageData = {
        jobId: 'test-job-123',
        invoiceId: 'invoice-456',
        tenantId: 'tenant-789',
        imageUrl: 'gs://bucket/image.jpg'
      };

      const mockMessage = createMockMessage(messageData);
      
      // Mock orchestrator throwing an exception
      mockOrchestratorProcessInvoice.mockRejectedValue(new Error('Network timeout'));

      // Process the message
      await messageHandler(mockMessage);

      // Verify message was nacked for retry
      expect(mockMessage.nack).toHaveBeenCalled();
      expect(mockMessage.ack).not.toHaveBeenCalled();
    });

    test('should handle invalid JSON in message', async () => {
      const mockMessage = {
        id: 'test-message-id',
        data: Buffer.from('invalid json'),
        ack: jest.fn(),
        nack: jest.fn()
      };

      // Process the message
      await messageHandler(mockMessage);

      // Verify OCR orchestrator was not called
      expect(mockOrchestratorProcessInvoice).not.toHaveBeenCalled();

      // Verify message was nacked
      expect(mockMessage.nack).toHaveBeenCalled();
      expect(mockMessage.ack).not.toHaveBeenCalled();
    });
  });

  describe('status', () => {
    test('should return correct status when stopped', () => {
      const status = worker.getStatus();
      
      expect(status.isRunning).toBe(false);
      expect(status.subscriptionName).toBe('test-subscription');
    });

    test('should return correct status when running', () => {
      worker.start();
      const status = worker.getStatus();
      
      expect(status.isRunning).toBe(true);
      expect(status.subscriptionName).toBe('test-subscription');
    });
  });
});
