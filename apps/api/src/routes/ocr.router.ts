import { z } from 'zod';
import { router, authProcedure } from '../trpc/trpc';
import { TRPCError } from '@trpc/server';
import { firebase } from '../config/firebase';
import { logger } from '../config/logger';
import { OcrTier } from '@billsnapp/shared-types';
import { OcrOrchestrator } from '../services/ocr/orchestrator';
import { Storage } from '@google-cloud/storage';
import { env } from '../config/env';
import { PubSub } from '@google-cloud/pubsub';

// Initialize Google Cloud Storage
const storage = new Storage();

// Initialize PubSub client
const pubsub = new PubSub();

// Create OCR orchestrator instance
const ocrOrchestrator = new OcrOrchestrator();

/**
 * OCR router for handling document processing operations
 */
export const ocrRouter = router({
  /**
   * Start an OCR job for an invoice
   * This is the main entry point for OCR processing
   */
  startOcrJob: authProcedure
    .input(z.object({
      invoiceId: z.string(),
      tenantId: z.string(),
      imageUrl: z.string(),
      originalFilename: z.string().optional(),
      vendorId: z.string().optional(),
      startingTier: z.enum([
        OcrTier.TIER_1,
        OcrTier.TIER_2,
        OcrTier.TIER_3
      ]).optional(),
      forceAllTiers: z.boolean().optional()
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // Verify tenant access
        await ctx.validateTenantAccess(input.tenantId);

        // Create a job ID for tracking
        const jobId = `ocr-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

        // Create OCR job document in Firestore
        const ocrJobRef = firebase.firestore.collection('ocrJobs').doc(jobId);
        await ocrJobRef.set({
          jobId,
          invoiceId: input.invoiceId,
          tenantId: input.tenantId,
          imageUrl: input.imageUrl,
          originalFilename: input.originalFilename || 'unknown',
          vendorId: input.vendorId,
          startingTier: input.startingTier || OcrTier.TIER_1,
          forceAllTiers: input.forceAllTiers || false,
          status: 'QUEUED',
          createdAt: new Date(),
          createdBy: ctx.user.uid
        });

        // Publish message to Pub/Sub for processing
        const topicName = env.PUBSUB_INVOICE_PROCESSING_TOPIC;
        const topic = pubsub.topic(topicName);

        const message = {
          jobId,
          invoiceId: input.invoiceId,
          tenantId: input.tenantId,
          imageUrl: input.imageUrl,
          originalFilename: input.originalFilename || 'unknown',
          vendorId: input.vendorId,
          startingTier: input.startingTier || OcrTier.TIER_1,
          forceAllTiers: input.forceAllTiers || false,
          userId: ctx.user.uid
        };

        const messageBuffer = Buffer.from(JSON.stringify(message));
        await topic.publish(messageBuffer);

        logger.info(`Started OCR job ${jobId} for invoice ${input.invoiceId}`);

        return {
          jobId,
          status: 'QUEUED',
          message: 'OCR job started successfully'
        };

      } catch (error) {
        logger.error('Error starting OCR job:', error);

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to start OCR job'
        });
      }
    }),
  /**
   * Process an invoice using the tiered OCR approach
   */
  processInvoice: authProcedure
    .input(z.object({
      invoiceId: z.string(),
      imageUrl: z.string().optional(),
      fileId: z.string().optional(),
      vendorId: z.string().optional(),
      startingTier: z.enum([
        OcrTier.TIER_1,
        OcrTier.TIER_2,
        OcrTier.TIER_3
      ]).optional(),
      forceAllTiers: z.boolean().optional()
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // Get the invoice to verify access
        const invoiceDoc = await firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .get();
        
        if (!invoiceDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Invoice not found'
          });
        }
        
        const invoiceData = invoiceDoc.data()!;
        
        // Verify tenant access with proper role
        await ctx.validateTenantAccess(invoiceData.tenantId, ['TenantAdmin', 'Accountant', 'InvoiceCapturer']);
        
        // Make sure we have either imageUrl or fileId
        let imageUrl = input.imageUrl;
        
        if (!imageUrl && input.fileId) {
          // Look up the file information
          const fileDoc = await firebase.firestore
            .collection('invoices')
            .doc(input.invoiceId)
            .collection('files')
            .doc(input.fileId)
            .get();
          
          if (!fileDoc.exists) {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: 'File not found'
            });
          }
          
          const fileData = fileDoc.data()!;
          imageUrl = fileData.storageUrl;
        }
        
        if (!imageUrl) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Either imageUrl or fileId must be provided'
          });
        }
        
        // Create a job ID for tracking
        const jobId = `ocr-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
        
        // Update invoice with processing status
        await firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .update({
            status: 'PROCESSING',
            updatedAt: new Date(),
            'processingDetails.jobId': jobId,
            'processingDetails.startedAt': new Date(),
            'processingDetails.status': 'PROCESSING'
          });
        
        // For immediate processing, use direct call
        if (env.OCR_PROCESSING_MODE === 'direct') {
          // Process immediately
          logger.info(`Starting direct OCR processing for invoice ${input.invoiceId}`);
          
          // Start processing in background to not block the API response
          ocrOrchestrator.processInvoice({
            invoiceId: input.invoiceId,
            tenantId: invoiceData.tenantId,
            imageUrl,
            originalFilename: input.fileId ? `file-${input.fileId}` : 'unknown',
            vendorId: input.vendorId,
            startingTier: input.startingTier,
            forceAllTiers: input.forceAllTiers
          }).catch(error => {
            logger.error(`Background OCR processing error for invoice ${input.invoiceId}:`, error);
          });
          
          return {
            jobId,
            invoiceId: input.invoiceId,
            status: 'PROCESSING'
          };
        } else {
          // Use Pub/Sub for async processing (recommended for production)
          const topicName = env.PUBSUB_INVOICE_PROCESSING_TOPIC || 'invoice-processing';
          const topic = pubsub.topic(topicName);
          
          const message = {
            jobId,
            invoiceId: input.invoiceId,
            tenantId: invoiceData.tenantId,
            imageUrl,
            originalFilename: input.fileId ? `file-${input.fileId}` : 'unknown',
            vendorId: input.vendorId,
            startingTier: input.startingTier,
            forceAllTiers: input.forceAllTiers,
            userId: ctx.user.uid
          };
          
          const messageBuffer = Buffer.from(JSON.stringify(message));
          await topic.publish(messageBuffer);
          
          logger.info(`Published OCR job for invoice ${input.invoiceId} to Pub/Sub`);
          
          return {
            jobId,
            invoiceId: input.invoiceId,
            status: 'QUEUED'
          };
        }
      } catch (error) {
        logger.error('Error initiating OCR processing:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to initiate OCR processing'
        });
      }
    }),

  /**
   * Get OCR processing status for an invoice
   */
  getProcessingStatus: authProcedure
    .input(z.object({
      invoiceId: z.string()
    }))
    .query(async ({ ctx, input }) => {
      try {
        // Get the invoice to verify access
        const invoiceDoc = await firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .get();
        
        if (!invoiceDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Invoice not found'
          });
        }
        
        const invoiceData = invoiceDoc.data()!;
        
        // Verify tenant access
        await ctx.validateTenantAccess(invoiceData.tenantId);
        
        // Get processing details
        const processingDetails = invoiceData.processingDetails || {};
        
        return {
          invoiceId: input.invoiceId,
          status: invoiceData.status,
          processingDetails: {
            jobId: processingDetails.jobId,
            startedAt: processingDetails.startedAt?.toDate(),
            completedAt: processingDetails.completedAt?.toDate(),
            currentTier: processingDetails.currentTier,
            successfulTier: processingDetails.successfulTier,
            error: processingDetails.error,
            confidence: processingDetails.confidence
          }
        };
      } catch (error) {
        logger.error('Error getting OCR processing status:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get OCR processing status'
        });
      }
    }),

  /**
   * Get a signed URL for uploading an invoice image
   */
  getUploadUrl: authProcedure
    .input(z.object({
      invoiceId: z.string(),
      fileName: z.string(),
      contentType: z.string(),
      sizeBytes: z.number().int().positive()
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // Get the invoice to verify access
        const invoiceDoc = await firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .get();
        
        if (!invoiceDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Invoice not found'
          });
        }
        
        const invoiceData = invoiceDoc.data()!;
        
        // Verify tenant access
        await ctx.validateTenantAccess(invoiceData.tenantId);
        
        // Sanitize filename
        const sanitizedFileName = input.fileName
          .replace(/[^a-zA-Z0-9._-]/g, '_')
          .toLowerCase();
        
        // Create a unique path for the file
        const timestamp = Date.now();
        const fileId = `${timestamp}-${Math.random().toString(36).substring(2, 9)}`;
        const filePath = `tenants/${invoiceData.tenantId}/invoices/${input.invoiceId}/${fileId}-${sanitizedFileName}`;
        
        // Get bucket from env or use default
        const bucketName = env.STORAGE_BUCKET || 'billsnapp-invoices';
        const bucket = storage.bucket(bucketName);
        
        // Generate signed URL for upload
        const [signedUrl] = await bucket.file(filePath).getSignedUrl({
          version: 'v4',
          action: 'write',
          expires: Date.now() + 15 * 60 * 1000, // 15 minutes
          contentType: input.contentType
        });
        
        // Create file record in Firestore
        const fileRef = await firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .collection('files')
          .doc(fileId);
        
        await fileRef.set({
          id: fileId,
          invoiceId: input.invoiceId,
          tenantId: invoiceData.tenantId,
          name: sanitizedFileName,
          originalName: input.fileName,
          contentType: input.contentType,
          sizeBytes: input.sizeBytes,
          storageUrl: `gs://${bucketName}/${filePath}`,
          uploadedBy: ctx.user.uid,
          uploadedAt: new Date(),
          status: 'PENDING'
        });
        
        return {
          uploadUrl: signedUrl,
          fileId,
          storageUrl: `gs://${bucketName}/${filePath}`
        };
      } catch (error) {
        logger.error('Error generating upload URL:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to generate upload URL'
        });
      }
    }),

  /**
   * Confirm that a file upload is complete
   */
  confirmUpload: authProcedure
    .input(z.object({
      invoiceId: z.string(),
      fileId: z.string()
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // Get the invoice to verify access
        const invoiceDoc = await firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .get();
        
        if (!invoiceDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Invoice not found'
          });
        }
        
        const invoiceData = invoiceDoc.data()!;
        
        // Verify tenant access
        await ctx.validateTenantAccess(invoiceData.tenantId);
        
        // Get the file record
        const fileRef = firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .collection('files')
          .doc(input.fileId);
        
        const fileDoc = await fileRef.get();
        
        if (!fileDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'File not found'
          });
        }
        
        const fileData = fileDoc.data()!;
        
        // Check if the file exists in storage
        const bucketName = fileData.storageUrl.match(/gs:\/\/([^\/]+)\/.*/)[1];
        const filePath = fileData.storageUrl.replace(`gs://${bucketName}/`, '');
        
        const [exists] = await storage.bucket(bucketName).file(filePath).exists();
        
        if (!exists) {
          throw new TRPCError({
            code: 'FAILED_PRECONDITION',
            message: 'File not found in storage'
          });
        }
        
        // Update file status
        await fileRef.update({
          status: 'UPLOADED',
          confirmedAt: new Date()
        });
        
        // Update invoice with file reference
        await firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .update({
            fileIds: firebase.firestore.FieldValue.arrayUnion(input.fileId),
            updatedAt: new Date()
          });
        
        return {
          success: true,
          fileId: input.fileId,
          status: 'UPLOADED'
        };
      } catch (error) {
        logger.error('Error confirming upload:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to confirm upload'
        });
      }
    }),

  /**
   * Request OCR retry with a specific tier
   */
  retryWithTier: authProcedure
    .input(z.object({
      invoiceId: z.string(),
      tier: z.enum([
        OcrTier.TIER_1,
        OcrTier.TIER_2,
        OcrTier.TIER_3
      ])
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // Get the invoice to verify access
        const invoiceDoc = await firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .get();
        
        if (!invoiceDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Invoice not found'
          });
        }
        
        const invoiceData = invoiceDoc.data()!;
        
        // Verify tenant access with proper role for retry
        await ctx.validateTenantAccess(invoiceData.tenantId, ['TenantAdmin', 'Accountant']);
        
        // Get the most recent file
        const filesQuery = await firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .collection('files')
          .orderBy('uploadedAt', 'desc')
          .limit(1)
          .get();
        
        if (filesQuery.empty) {
          throw new TRPCError({
            code: 'FAILED_PRECONDITION',
            message: 'No files found for this invoice'
          });
        }
        
        const fileData = filesQuery.docs[0].data();
        const fileId = filesQuery.docs[0].id;
        
        // Create a new job ID for tracking
        const jobId = `ocr-retry-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
        
        // Update invoice with processing status
        await firebase.firestore
          .collection('invoices')
          .doc(input.invoiceId)
          .update({
            status: 'PROCESSING',
            updatedAt: new Date(),
            'processingDetails.jobId': jobId,
            'processingDetails.startedAt': new Date(),
            'processingDetails.status': 'PROCESSING',
            'processingDetails.isRetry': true,
            'processingDetails.requestedTier': input.tier
          });
        
        // For immediate processing, use direct call
        if (env.OCR_PROCESSING_MODE === 'direct') {
          // Process immediately
          logger.info(`Starting direct OCR retry for invoice ${input.invoiceId} with tier ${input.tier}`);
          
          // Start processing in background
          ocrOrchestrator.processInvoice({
            invoiceId: input.invoiceId,
            tenantId: invoiceData.tenantId,
            imageUrl: fileData.storageUrl,
            originalFilename: `file-${fileId}`,
            vendorId: invoiceData.vendorId,
            startingTier: input.tier,
            forceAllTiers: false
          }).catch(error => {
            logger.error(`Background OCR retry error for invoice ${input.invoiceId}:`, error);
          });
          
          return {
            jobId,
            invoiceId: input.invoiceId,
            status: 'PROCESSING',
            tier: input.tier
          };
        } else {
          // Use Pub/Sub for async processing
          const topicName = env.PUBSUB_INVOICE_PROCESSING_TOPIC || 'invoice-processing';
          const topic = pubsub.topic(topicName);
          
          const message = {
            jobId,
            invoiceId: input.invoiceId,
            tenantId: invoiceData.tenantId,
            imageUrl: fileData.storageUrl,
            originalFilename: `file-${fileId}`,
            vendorId: invoiceData.vendorId,
            startingTier: input.tier,
            forceAllTiers: false,
            userId: ctx.user.uid,
            isRetry: true
          };
          
          const messageBuffer = Buffer.from(JSON.stringify(message));
          await topic.publish(messageBuffer);
          
          logger.info(`Published OCR retry job for invoice ${input.invoiceId} with tier ${input.tier} to Pub/Sub`);
          
          return {
            jobId,
            invoiceId: input.invoiceId,
            status: 'QUEUED',
            tier: input.tier
          };
        }
      } catch (error) {
        logger.error('Error initiating OCR retry:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to initiate OCR retry'
        });
      }
    })
});
