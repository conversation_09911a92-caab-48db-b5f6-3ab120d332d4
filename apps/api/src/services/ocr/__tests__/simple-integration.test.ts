/// <reference types="jest" />

import { OcrOrchestrator } from '../orchestrator';
import { OcrTier } from '@billsnapp/shared-types';

// Mock all external dependencies
jest.mock('@google-cloud/vision');
jest.mock('@google-cloud/documentai');
jest.mock('@google-cloud/storage');
jest.mock('@google-cloud/vertexai');
jest.mock('axios');

describe('OCR System Integration', () => {
  let orchestrator: OcrOrchestrator;

  beforeEach(() => {
    orchestrator = new OcrOrchestrator();
  });

  describe('Basic Functionality', () => {
    test('should create orchestrator instance', () => {
      expect(orchestrator).toBeDefined();
      expect(orchestrator).toBeInstanceOf(OcrOrchestrator);
    });

    test('should handle invalid image URL gracefully', async () => {
      const options = {
        invoiceId: 'test-invoice-123',
        tenantId: 'test-tenant-456',
        imageUrl: 'invalid://url',
        originalFilename: 'test.pdf'
      };

      const result = await orchestrator.processInvoice(options);

      expect(result).toBeDefined();
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(typeof result.error).toBe('string');
    });

    test('should handle missing required options', async () => {
      const options = {
        invoiceId: '',
        tenantId: '',
        imageUrl: '',
        originalFilename: ''
      };

      const result = await orchestrator.processInvoice(options);

      expect(result).toBeDefined();
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    test('should handle network timeouts gracefully', async () => {
      const options = {
        invoiceId: 'test-invoice-timeout',
        tenantId: 'test-tenant-timeout',
        imageUrl: 'gs://test-bucket/timeout.jpg',
        originalFilename: 'timeout.pdf'
      };

      // This will fail in test environment due to mocked services
      // but should not throw unhandled exceptions
      const result = await orchestrator.processInvoice(options);

      expect(result).toBeDefined();
      expect(typeof result.success).toBe('boolean');
      
      if (!result.success) {
        expect(result.error).toBeDefined();
        expect(typeof result.error).toBe('string');
      }
    });
  });

  describe('Configuration', () => {
    test('should handle missing environment variables', () => {
      // Test that the system doesn't crash when env vars are missing
      expect(() => new OcrOrchestrator()).not.toThrow();
    });

    test('should support different starting tiers', async () => {
      const baseOptions = {
        invoiceId: 'test-invoice-tier',
        tenantId: 'test-tenant-tier',
        imageUrl: 'gs://test-bucket/tier.jpg',
        originalFilename: 'tier.pdf'
      };

      // Test each tier as starting point
      for (const tier of [OcrTier.TIER_1, OcrTier.TIER_2, OcrTier.TIER_3]) {
        const options = { ...baseOptions, startingTier: tier };
        const result = await orchestrator.processInvoice(options);
        
        expect(result).toBeDefined();
        expect(typeof result.success).toBe('boolean');
      }
    });

    test('should support forceAllTiers option', async () => {
      const options = {
        invoiceId: 'test-invoice-all-tiers',
        tenantId: 'test-tenant-all-tiers',
        imageUrl: 'gs://test-bucket/all-tiers.jpg',
        originalFilename: 'all-tiers.pdf',
        forceAllTiers: true
      };

      const result = await orchestrator.processInvoice(options);

      expect(result).toBeDefined();
      expect(typeof result.success).toBe('boolean');
      
      // When forceAllTiers is true, should have allTierResults
      if (result.allTierResults) {
        expect(Array.isArray(result.allTierResults)).toBe(true);
      }
    });
  });

  describe('Error Handling', () => {
    test('should not throw unhandled exceptions', async () => {
      const problematicOptions = [
        // Null/undefined values
        { invoiceId: null, tenantId: null, imageUrl: null, originalFilename: null },
        // Very long strings
        { 
          invoiceId: 'x'.repeat(1000), 
          tenantId: 'y'.repeat(1000), 
          imageUrl: 'z'.repeat(1000), 
          originalFilename: 'w'.repeat(1000) 
        },
        // Special characters
        { 
          invoiceId: '🚀💥', 
          tenantId: '<script>alert("xss")</script>', 
          imageUrl: 'javascript:alert(1)', 
          originalFilename: '../../etc/passwd' 
        }
      ];

      for (const options of problematicOptions) {
        await expect(async () => {
          await orchestrator.processInvoice(options as any);
        }).not.toThrow();
      }
    });

    test('should return consistent error structure', async () => {
      const options = {
        invoiceId: 'test-error-structure',
        tenantId: 'test-error-structure',
        imageUrl: 'invalid://url',
        originalFilename: 'error.pdf'
      };

      const result = await orchestrator.processInvoice(options);

      expect(result).toHaveProperty('success');
      expect(typeof result.success).toBe('boolean');
      
      if (!result.success) {
        expect(result).toHaveProperty('error');
        expect(typeof result.error).toBe('string');
        expect(result.error.length).toBeGreaterThan(0);
      }
    });
  });

  describe('Performance', () => {
    test('should complete within reasonable time', async () => {
      const startTime = Date.now();
      
      const options = {
        invoiceId: 'test-performance',
        tenantId: 'test-performance',
        imageUrl: 'gs://test-bucket/performance.jpg',
        originalFilename: 'performance.pdf'
      };

      await orchestrator.processInvoice(options);
      
      const duration = Date.now() - startTime;
      
      // Should complete within 30 seconds even with all the mocked failures
      expect(duration).toBeLessThan(30000);
    });

    test('should handle concurrent requests', async () => {
      const concurrentRequests = 3;
      const requests = Array.from({ length: concurrentRequests }, (_, i) => ({
        invoiceId: `test-concurrent-${i}`,
        tenantId: `test-concurrent-${i}`,
        imageUrl: `gs://test-bucket/concurrent-${i}.jpg`,
        originalFilename: `concurrent-${i}.pdf`
      }));

      const startTime = Date.now();
      
      const results = await Promise.all(
        requests.map(options => orchestrator.processInvoice(options))
      );
      
      const duration = Date.now() - startTime;

      // All requests should complete
      expect(results).toHaveLength(concurrentRequests);
      
      // Each result should have the expected structure
      results.forEach(result => {
        expect(result).toHaveProperty('success');
        expect(typeof result.success).toBe('boolean');
      });

      // Concurrent processing should not take much longer than sequential
      expect(duration).toBeLessThan(60000);
    });
  });
});
