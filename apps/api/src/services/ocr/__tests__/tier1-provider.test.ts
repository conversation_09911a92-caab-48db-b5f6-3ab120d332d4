import { Tier1OcrProvider } from '../tier1-provider';
import { OcrProcessingOptions, OcrProcessingResult, OcrTier } from '../types';
import * as vision from '@google-cloud/vision';

// Mock the Google Cloud Vision API
jest.mock('@google-cloud/vision');

describe('Tier1OcrProvider', () => {
  let provider: Tier1OcrProvider;
  let mockVisionClient: jest.Mocked<vision.ImageAnnotatorClient>;
  
  const mockOptions: OcrProcessingOptions = {
    invoiceId: 'test-invoice-id',
    tenantId: 'test-tenant-id',
    imageUrl: 'gs://test-bucket/test-invoice.jpg',
    originalFilename: 'test-invoice.jpg'
  };
  
  beforeEach(() => {
    // Reset all mocks
    jest.resetAllMocks();
    
    // Setup mock Vision client
    mockVisionClient = {
      textDetection: jest.fn(),
      documentTextDetection: jest.fn(),
    } as unknown as jest.Mocked<vision.ImageAnnotatorClient>;
    
    // Create provider with mocked client
    provider = new Tier1OcrProvider();
    (provider as any).visionClient = mockVisionClient;
  });
  
  test('should successfully process invoice with Gemma model', async () => {
    // Mock Vision API response
    const mockTextDetectionResult = [
      {
        fullTextAnnotation: {
          text: 'Invoice #INV-123\nVendor: Test Company Inc.\nDate: 01/01/2023\nAmount: $100.00\nTax: $10.00\nTotal: $110.00'
        }
      }
    ];
    
    mockVisionClient.textDetection.mockResolvedValue(mockTextDetectionResult as any);
    
    // Mock internal method responses
    jest.spyOn(provider as any, 'processWithGemma').mockResolvedValue({
      success: true,
      extractedData: {
        vendorName: 'Test Company Inc.',
        invoiceNumber: 'INV-123',
        issueDate: '2023-01-01',
        dueDate: '2023-02-01',
        totalAmount: 110.00,
        taxAmount: 10.00,
        currency: 'USD',
        lineItems: [
          {
            description: 'Service',
            quantity: 1,
            unitPrice: 100.00,
            amount: 100.00
          }
        ]
      },
      confidence: {
        vendorName: 0.95,
        invoiceNumber: 0.92,
        issueDate: 0.90,
        totalAmount: 0.98
      },
      metadata: {
        processingTimeMs: 1000
      }
    });
    
    // Execute the test
    const result = await provider.processInvoice(mockOptions);
    
    // Verify results
    expect(result.success).toBe(true);
    expect(result.extractedData).toBeDefined();
    expect(result.extractedData?.vendorName).toBe('Test Company Inc.');
    expect(result.extractedData?.invoiceNumber).toBe('INV-123');
    expect(result.confidence).toBeDefined();
    expect(result.metadata?.processingTimeMs).toBeGreaterThan(0);
    
    // Verify method calls
    expect(mockVisionClient.textDetection).toHaveBeenCalledWith(mockOptions.imageUrl);
  });
  
  test('should handle extraction errors gracefully', async () => {
    // Mock Vision API to throw an error
    mockVisionClient.textDetection.mockRejectedValue(new Error('API Error'));
    
    // Execute the test
    const result = await provider.processInvoice(mockOptions);
    
    // Verify error handling
    expect(result.success).toBe(false);
    expect(result.error).toContain('Error extracting text');
    expect(result.metadata?.processingTimeMs).toBeGreaterThan(0);
  });
  
  test('should handle empty text extraction results', async () => {
    // Mock Vision API with empty result
    mockVisionClient.textDetection.mockResolvedValue([{}] as any);
    
    // Execute the test
    const result = await provider.processInvoice(mockOptions);
    
    // Verify error handling
    expect(result.success).toBe(false);
    expect(result.error).toContain('No text detected');
    expect(result.metadata?.processingTimeMs).toBeGreaterThan(0);
  });
  
  test('should handle Gemma model failures', async () => {
    // Mock Vision API with valid result
    const mockTextDetectionResult = [
      {
        fullTextAnnotation: {
          text: 'Invoice #INV-123\nVendor: Test Company Inc.\nDate: 01/01/2023\nAmount: $100.00'
        }
      }
    ];
    
    mockVisionClient.textDetection.mockResolvedValue(mockTextDetectionResult as any);
    
    // Mock Gemma model failure
    jest.spyOn(provider as any, 'processWithGemma').mockResolvedValue({
      success: false,
      error: 'Model inference error',
      metadata: {
        processingTimeMs: 500
      }
    });

    // Execute the test
    const result = await provider.processInvoice(mockOptions);

    // Verify error handling
    expect(result.success).toBe(false);
    expect(result.error).toContain('Model inference error');
    expect(result.metadata?.processingTimeMs).toBeGreaterThan(0);
  });
});
