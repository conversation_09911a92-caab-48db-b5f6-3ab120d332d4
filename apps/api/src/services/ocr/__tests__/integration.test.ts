/// <reference types="jest" />

import { OcrOrchestrator } from '../orchestrator';
import { OcrTier } from '@billsnapp/shared-types';
import { createServer, Server } from 'http';
import { AddressInfo } from 'net';

// Mock external services with HTTP servers
describe('OCR Integration Tests', () => {
  let orchestrator: OcrOrchestrator;
  let gemmaServer: Server;
  let gemmaPort: number;

  beforeAll(async () => {
    // Create mock Gemma OCR server
    gemmaServer = createServer((req, res) => {
      if (req.method === 'POST' && req.url === '/run') {
        let body = '';
        req.on('data', chunk => {
          body += chunk.toString();
        });
        
        req.on('end', () => {
          try {
            const requestData = JSON.parse(body);
            
            // Simulate successful OCR response
            const mockResponse = {
              success: true,
              extracted_data: {
                vendor_name: 'Mock Vendor Inc.',
                invoice_number: 'INV-2024-001',
                issue_date: '2024-01-15',
                due_date: '2024-02-15',
                total_amount: 1250.00,
                tax_amount: 125.00,
                currency: 'USD',
                line_items: [
                  {
                    description: 'Professional Services',
                    quantity: 10,
                    unit_price: 112.50,
                    amount: 1125.00
                  }
                ]
              },
              confidence_scores: {
                vendor_name: 0.95,
                invoice_number: 0.92,
                issue_date: 0.88,
                total_amount: 0.96
              },
              processing_time_ms: 1500
            };

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify(mockResponse));
          } catch (error) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Invalid request' }));
          }
        });
      } else {
        res.writeHead(404);
        res.end('Not found');
      }
    });

    // Start server on random port
    await new Promise<void>((resolve) => {
      gemmaServer.listen(0, () => {
        gemmaPort = (gemmaServer.address() as AddressInfo).port;
        resolve();
      });
    });

    // Create orchestrator with mocked endpoints
    orchestrator = new OcrOrchestrator();
    
    // Override environment variables for testing
    process.env.GEMMA_OCR_ENDPOINT = `http://localhost:${gemmaPort}`;
    process.env.GEMMA_OCR_API_KEY = 'test-api-key';
  });

  afterAll(async () => {
    if (gemmaServer) {
      await new Promise<void>((resolve) => {
        gemmaServer.close(() => resolve());
      });
    }
  });

  describe('End-to-End OCR Processing', () => {
    test('should successfully process invoice through Tier 1', async () => {
      const options = {
        invoiceId: 'test-invoice-123',
        tenantId: 'test-tenant-456',
        imageUrl: 'gs://test-bucket/invoice.jpg',
        originalFilename: 'invoice.pdf',
        startingTier: OcrTier.TIER_1
      };

      const result = await orchestrator.processInvoice(options);

      expect(result.success).toBe(true);
      expect(result.successfulTier).toBe(OcrTier.TIER_1);
      expect(result.extractedData).toBeDefined();
      expect(result.extractedData?.vendorName).toBe('Mock Vendor Inc.');
      expect(result.extractedData?.invoiceNumber).toBe('INV-2024-001');
      expect(result.extractedData?.totalAmount).toBe(1250.00);
      expect(result.confidence).toBeDefined();
      expect(result.processingTimeMs).toBeGreaterThan(0);
    });

    test('should handle Tier 1 failure and fallback to Tier 2', async () => {
      // Temporarily break Gemma server
      gemmaServer.close();

      const options = {
        invoiceId: 'test-invoice-456',
        tenantId: 'test-tenant-789',
        imageUrl: 'gs://test-bucket/invoice2.jpg',
        originalFilename: 'invoice2.pdf',
        startingTier: OcrTier.TIER_1
      };

      const result = await orchestrator.processInvoice(options);

      // Should fallback to Tier 2 (Vertex AI)
      // Note: This will likely fail in test environment without proper Vertex AI setup
      // but we can verify the fallback logic is triggered
      expect(result.success).toBe(false); // Expected in test environment
      expect(result.error).toContain('All OCR tiers failed');
    });

    test('should process with forceAllTiers option', async () => {
      // Restart Gemma server
      await new Promise<void>((resolve) => {
        gemmaServer.listen(gemmaPort, () => resolve());
      });

      const options = {
        invoiceId: 'test-invoice-789',
        tenantId: 'test-tenant-123',
        imageUrl: 'gs://test-bucket/invoice3.jpg',
        originalFilename: 'invoice3.pdf',
        startingTier: OcrTier.TIER_1,
        forceAllTiers: true
      };

      const result = await orchestrator.processInvoice(options);

      expect(result.success).toBe(true);
      expect(result.allTierResults).toBeDefined();
      expect(result.allTierResults).toHaveLength(3); // All three tiers attempted
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid image URL', async () => {
      const options = {
        invoiceId: 'test-invoice-error',
        tenantId: 'test-tenant-error',
        imageUrl: 'invalid://url',
        originalFilename: 'invalid.pdf'
      };

      const result = await orchestrator.processInvoice(options);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    test('should handle network timeouts', async () => {
      // Create a server that never responds
      const timeoutServer = createServer((req, res) => {
        // Never respond to simulate timeout
      });

      const timeoutPort = await new Promise<number>((resolve) => {
        timeoutServer.listen(0, () => {
          resolve((timeoutServer.address() as AddressInfo).port);
        });
      });

      // Override endpoint to timeout server
      process.env.GEMMA_OCR_ENDPOINT = `http://localhost:${timeoutPort}`;

      const options = {
        invoiceId: 'test-invoice-timeout',
        tenantId: 'test-tenant-timeout',
        imageUrl: 'gs://test-bucket/timeout.jpg',
        originalFilename: 'timeout.pdf'
      };

      const result = await orchestrator.processInvoice(options);

      expect(result.success).toBe(false);
      expect(result.error).toContain('timeout');

      // Cleanup
      timeoutServer.close();
      process.env.GEMMA_OCR_ENDPOINT = `http://localhost:${gemmaPort}`;
    });
  });

  describe('Performance Tests', () => {
    test('should complete processing within reasonable time', async () => {
      const startTime = Date.now();

      const options = {
        invoiceId: 'test-invoice-perf',
        tenantId: 'test-tenant-perf',
        imageUrl: 'gs://test-bucket/perf.jpg',
        originalFilename: 'perf.pdf'
      };

      const result = await orchestrator.processInvoice(options);
      const totalTime = Date.now() - startTime;

      expect(result.success).toBe(true);
      expect(totalTime).toBeLessThan(10000); // Should complete within 10 seconds
      expect(result.processingTimeMs).toBeLessThan(totalTime);
    });

    test('should handle concurrent processing', async () => {
      const concurrentJobs = Array.from({ length: 5 }, (_, i) => ({
        invoiceId: `test-invoice-concurrent-${i}`,
        tenantId: `test-tenant-concurrent-${i}`,
        imageUrl: `gs://test-bucket/concurrent-${i}.jpg`,
        originalFilename: `concurrent-${i}.pdf`
      }));

      const startTime = Date.now();
      const results = await Promise.all(
        concurrentJobs.map(options => orchestrator.processInvoice(options))
      );
      const totalTime = Date.now() - startTime;

      // All jobs should succeed
      results.forEach(result => {
        expect(result.success).toBe(true);
      });

      // Concurrent processing should be faster than sequential
      expect(totalTime).toBeLessThan(15000); // Should complete within 15 seconds
    });
  });
});
