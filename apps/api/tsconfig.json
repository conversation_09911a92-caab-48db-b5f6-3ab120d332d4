{
  "extends": "@billsnapp/tsconfig/node-app.json",
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@config/*": ["./src/config/*"],
      "@ocr-types/*": ["./src/services/ocr/*"],
      "@ocr-quality-check/*": ["./src/services/ocr/*"],
      "@ocr-orchestrator/*": ["./src/services/ocr/*"], // Alias for orchestrator service
      "@repo/shared-types": ["../../packages/shared-types/src/index.ts"],
      "@repo/shared-types/*": ["../../packages/shared-types/src/*"],
      "@ocr-providers/*": ["./src/services/ocr/*"]
    }
  },
  "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.json", ".eslintrc.js"]
}
